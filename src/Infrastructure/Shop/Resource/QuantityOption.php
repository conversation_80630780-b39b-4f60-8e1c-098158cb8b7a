<?php

declare(strict_types=1);

namespace App\Infrastructure\Shop\Resource;

use App\Infrastructure\Shop\HttpEndpoint\QuantityOptionEndpoint;
use PreZero\ApiBundle\Attribute\ApiResource;
use PreZero\ApiBundle\Attribute\Operation\GetCollection;
use PreZero\ApiBundle\Attribute\Value\Filter;
use PreZero\ApiBundle\Attribute\Value\PathParameter;
use PreZero\ApiBundle\Enum\Pagination;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Attribute\SerializedName;

#[ApiResource(
    area: 'shop',
    operations: [
        new GetCollection(
            controller: [QuantityOptionEndpoint::class, 'getOptions'],
            uriTemplate: '/product-types/{productType}/quantity-options/{quantityOption}/options',
            pathParameters: [
                new PathParameter(name: 'productType'),
                new PathParameter(name: 'quantityOption'),
            ],
            filters: [
                new Filter(
                    parameterName: 'selectedParentQuantityContentOptionId',
                    parameterDescription: 'Filter for selected parent quantity content option.',
                    parameterFormat: 'string',
                ),
            ],
            pagination: Pagination::NONE,
            normalizationContext: ['groups' => ['options']],
            responseOpenApiSchemaName: 'QuantityOptionListReponse',
        ),
    ],
    identifier: 'id',
    tag: 'QuantitySelection',
)]
class QuantityOption
{
    #[SerializedName('id')]
    #[Groups(['options'])]
    public string $id;

    #[SerializedName('display')]
    #[Groups(['options'])]
    public string $display;

    #[SerializedName('tooltip')]
    #[Groups(['options'])]
    public string $tooltip;

    public ?string $sequence = null;
}
