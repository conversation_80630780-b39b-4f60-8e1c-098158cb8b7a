<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Domain\Entity\Interface\EntityInterface;
use App\Domain\Entity\Trait\CommonTrait;
use App\Domain\Repository\QuantityOptionContentRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: QuantityOptionContentRepository::class)]
class QuantityOptionContent implements EntityInterface
{
    use CommonTrait;

    #[ORM\Column(type: Types::GUID, nullable: true)]
    private ?string $parentId = null;

    #[ORM\Column(length: 50)]
    private string $name;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $description = null;

    #[ORM\Column(length: 100, nullable: true)]
    private ?string $tooltip = null;

    #[ORM\Column(length: 3, nullable: true)]
    private ?string $billingUnit = null;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $externalId = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $externalDescription = null;

    #[ORM\Column(nullable: true)]
    private ?float $price = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $category = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $sequence = null;

    #[ORM\ManyToOne(inversedBy: 'quantityOptionContents')]
    #[ORM\JoinColumn(nullable: false)]
    private QuantityOption $quantityOption;

    public function __construct()
    {
        $this->init();
    }

    public function getParentId(): ?string
    {
        return $this->parentId;
    }

    public function setParentId(?string $parentId): static
    {
        $this->parentId = $parentId;

        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getTooltip(): ?string
    {
        return $this->tooltip;
    }

    public function setTooltip(?string $tooltip): static
    {
        $this->tooltip = $tooltip;

        return $this;
    }

    public function getBillingUnit(): ?string
    {
        return $this->billingUnit;
    }

    public function setBillingUnit(?string $billingUnit): static
    {
        $this->billingUnit = $billingUnit;

        return $this;
    }

    public function getExternalId(): ?string
    {
        return $this->externalId;
    }

    public function setExternalId(?string $externalId): static
    {
        $this->externalId = $externalId;

        return $this;
    }

    public function getExternalDescription(): ?string
    {
        return $this->externalDescription;
    }

    public function setExternalDescription(?string $externalDescription): static
    {
        $this->externalDescription = $externalDescription;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getSequence(): ?int
    {
        return $this->sequence;
    }

    public function setSequence(?int $sequence): static
    {
        $this->sequence = $sequence;

        return $this;
    }

    public function getCategory(): ?string
    {
        return $this->category;
    }

    public function setCategory(?string $category): static
    {
        $this->category = $category;

        return $this;
    }

    public function getPrice(): ?float
    {
        return $this->price;
    }

    public function setPrice(?float $price): static
    {
        $this->price = $price;

        return $this;
    }

    public function getQuantityOption(): QuantityOption
    {
        return $this->quantityOption;
    }

    public function setQuantityOption(QuantityOption $quantityOption): static
    {
        $this->quantityOption = $quantityOption;

        return $this;
    }
}
